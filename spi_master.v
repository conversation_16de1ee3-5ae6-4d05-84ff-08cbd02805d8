// SPI Master Module for Valve Control Protocol
// 支持SPI模式0 (CPOL=0, CPHA=0)
// 系统时钟125MHz，SPI时钟25MHz
// 根据阀控制协议发送数据包：起始标志(0xAA) + 64位阀状态 + CRC校验
module spi_master #(
    parameter SYS_CLK_FREQ = 125_000_000, // 系统时钟频率 125MHz
    parameter SPI_CLK_FREQ = 25_000_000   // SPI时钟频率 25MHz
)(
    // 系统接口
    input wire                    sys_clk,     // 系统时钟 125MHz
    input wire                    rst_n,       // 异步复位 (低有效)

    // 来自LVDS RX的接口
    input wire                    rx_valid,    // 接收数据有效标志
    input wire [63:0]             valve_data,  // 64位阀状态数据

    // SPI接口
    output reg                    spi_clk,     // SPI时钟
    output reg                    spi_cs_n,    // SPI片选 (低有效)
    output reg                    spi_mosi,    // SPI主出从入
    input wire                    spi_miso,    // SPI主入从出 (可选)

    // 状态接口
    output reg                    tx_busy,     // 发送忙标志
    output reg                    tx_done      // 发送完成脉冲
);

// 时钟分频参数计算
localparam CLK_DIV = SYS_CLK_FREQ / (2 * SPI_CLK_FREQ); // 125M/(2*25M) = 2.5, 取整为3
localparam CLK_DIV_CNT_WIDTH = $clog2(CLK_DIV);

// 协议常量定义
localparam HEADER = 8'hAA;               // 起始标志


// 内部信号定义
reg [CLK_DIV_CNT_WIDTH-1:0] clk_div_cnt;    // 时钟分频计数器
reg                         spi_clk_en;      // SPI时钟使能
wire                         spi_clk_pos;     // SPI时钟上升沿
wire                         spi_clk_neg;     // SPI时钟下降沿
reg                         spi_clk_d1;      // SPI时钟延迟1拍

reg [79:0]                  tx_shift_reg;    // 发送移位寄存器 (80位数据包)
reg [6:0]                   bit_cnt;         // 位计数器
reg                         rx_valid_d1;     // rx_valid延迟1拍
wire                        rx_valid_pos;    // rx_valid上升沿

// CRC计算相关
reg [7:0]                   crc_reg;         // CRC寄存器
wire [7:0]                  calculated_crc;  // 计算得到的CRC
reg [71:0]                  crc_data;        // 用于CRC计算的数据 (起始标志+阀状态)

// 状态机定义
localparam IDLE       = 3'd1;
localparam WAIT_CRC   = 3'd2;
localparam LOAD_DATA  = 3'd3;
localparam TRANSMIT   = 3'd4;
localparam FINISH     = 3'd5;

reg [1:0] state, next_state;

// rx_valid上升沿检测
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        rx_valid_d1 <= 1'b0;
    end else begin
        rx_valid_d1 <= rx_valid;
    end
end

assign rx_valid_pos = rx_valid & ~rx_valid_d1;

// CRC-8计算函数 (多项式: x^8 + x^2 + x^1 + 1 = 0x07)
function [7:0] calc_crc8;
    input [71:0] data;  // 9字节数据 (起始标志 + 8字节阀状态)
    integer i;
    reg [7:0] crc;
    reg [7:0] byte_data;
    integer j;
begin
    crc = 8'h00;  // 初始值
    for (i = 0; i < 9; i = i + 1) begin
        byte_data = data[71-i*8 -: 8];  // 从高位开始取字节
        crc = crc ^ byte_data;
        for (j = 0; j < 8; j = j + 1) begin
            if (crc[7])
                crc = (crc << 1) ^ 8'h07;  // 多项式0x07
            else
                crc = crc << 1;
        end
    end
    calc_crc8 = crc;
end
endfunction

assign calculated_crc = calc_crc8(crc_data);

// SPI时钟生成 (25MHz)
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        clk_div_cnt <= {CLK_DIV_CNT_WIDTH{1'b0}};
        spi_clk_en <= 1'b0;
    end else begin
        if (tx_busy) begin
            if (clk_div_cnt >= CLK_DIV - 1) begin
                clk_div_cnt <= {CLK_DIV_CNT_WIDTH{1'b0}};
                spi_clk_en <= 1'b1;
            end else begin
                clk_div_cnt <= clk_div_cnt + 1'b1;
                spi_clk_en <= 1'b0;
            end
        end else begin
            clk_div_cnt <= {CLK_DIV_CNT_WIDTH{1'b0}};
            spi_clk_en <= 1'b0;
        end
    end
end

// SPI时钟输出 (模式0: CPOL=0, CPHA=0)
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        spi_clk <= 1'b0;
        spi_clk_d1 <= 1'b0;
    end else begin
        spi_clk_d1 <= spi_clk;
        if (tx_busy && spi_clk_en) begin
            spi_clk <= ~spi_clk;
        end else if (!tx_busy) begin
            spi_clk <= 1'b0;  // 空闲时保持低电平 (CPOL=0)
        end
    end
end

// SPI时钟边沿检测
assign spi_clk_pos = spi_clk & ~spi_clk_d1;
assign spi_clk_neg = ~spi_clk & spi_clk_d1;

// 状态机 - 状态转换
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= IDLE;
    end else begin
        state <= next_state;
    end
end

// 状态机 - 下一状态逻辑
always @(*) begin
    case (state)
        IDLE: begin
            if (rx_valid_pos) begin
                next_state = WAIT_CRC;
            end else begin
                next_state = IDLE;
            end
        end

        WAIT_CRC: begin
            next_state = LOAD_DATA;
        end
        
        LOAD_DATA: begin
            next_state = TRANSMIT;
        end
        
        TRANSMIT: begin
            if (bit_cnt == 0 && spi_clk_neg) begin
                next_state = FINISH;
            end else begin
                next_state = TRANSMIT;
            end
        end
        
        FINISH: begin
            next_state = IDLE;
        end
        
        default: begin
            next_state = IDLE;
        end
    endcase
end

// 状态机 - 输出逻辑
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_shift_reg <= 80'd0;
        bit_cnt <= 7'd80;
        spi_cs_n <= 1'b1;
        spi_mosi <= 1'b0;
        tx_busy <= 1'b0;
        tx_done <= 1'b0;
        crc_data <= 72'h0;
        crc_reg <= 8'h00;
    end else begin
        tx_done <= 1'b0;  // 默认清除tx_done

        case (state)
            IDLE: begin
                spi_cs_n <= 1'b1;
                spi_mosi <= 1'b0;
                tx_busy <= 1'b0;
                bit_cnt <= 7'd80;
                if(rx_valid_pos) begin
                    crc_data <= {HEADER, valve_data};  // 用于CRC计算的数据
                end
            end

            WAIT_CRC: begin
                crc_reg <= calculated_crc;
            end

            LOAD_DATA: begin
                // 构建数据包: 起始标志(8位) + 阀状态(64位) + CRC(8位)
                tx_shift_reg <= {HEADER, valve_data, crc_reg};  // 完整数据包
                bit_cnt <= 7'd80;
                spi_cs_n <= 1'b0;        // 激活片选
                tx_busy <= 1'b1;
                spi_mosi <= HEADER[7]; // 预设第一位数据 (起始标志的最高位)
            end
            
            TRANSMIT: begin
                tx_busy <= 1'b1;
                spi_cs_n <= 1'b0;

                // SPI模式0: 在时钟上升沿采样，下降沿改变数据
                if (spi_clk_neg && bit_cnt > 0) begin
                    bit_cnt <= bit_cnt - 1'b1;
                    tx_shift_reg <= {tx_shift_reg[78:0], 1'b0};
                    if (bit_cnt > 1) begin
                        spi_mosi <= tx_shift_reg[78];
                    end else begin
                        spi_mosi <= 1'b0;
                    end
                end
            end
            
            FINISH: begin
                spi_cs_n <= 1'b1;
                spi_mosi <= 1'b0;
                tx_busy <= 1'b0;
                tx_done <= 1'b1;  // 发送完成脉冲
            end
            
            default: begin
                spi_cs_n <= 1'b1;
                spi_mosi <= 1'b0;
                tx_busy <= 1'b0;
            end
        endcase
    end
end

endmodule
