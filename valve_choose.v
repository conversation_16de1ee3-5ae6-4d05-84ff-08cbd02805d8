module valve_select(
    input wire [63:0] rx_valve,      // 输入源信号
    input wire        valve_sel,     // 选择信号（1位）
    output reg [63:0] mac_valve,     // 输出通道1
    output reg [63:0] matrix_valve   // 输出通道2
);

// 组合逻辑赋值
always @(*) begin
    if (valve_sel == 1'b0) begin
        mac_valve    = rx_valve;   // 选择MAC通道
        matrix_valve = 64'd0;      
    end
    else begin
        matrix_valve = rx_valve;   // 选择Matrix通道
        mac_valve    = 64'd0;     
    end
end

endmodule