module lvds_rx (
    input               sys_clk,        // FPGA系统时钟 125MHz
    input               rst_n,          // 异步复位，低电平有效
    input      [3:0]    lvds_in,       // LVDS输入信号(包含随路时钟和数据)
    output reg          rx_valid,       // 接收数据有效标志
    output reg [63:0]   rx_data         // 接收到的64位数据
);

// 内部信号定义
reg [63:0] rx_shift_reg;    // 移位寄存器
reg [6:0]  bit_cnt;         // 接收位计数器
reg        lvds_clk_d1;     // 随路时钟延迟1拍
wire       lvds_clk_pos;    // 随路时钟上升沿
wire       lvds_clk_neg;    // 随路时钟下降沿
wire       lvds_clk;        // 随路时钟（LVDS输入的第0位）

// 随路时钟和数据分离
assign lvds_clk = lvds_in[0];

// 检测随路时钟边沿
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        lvds_clk_d1 <= 1'b0;
    end else begin
        lvds_clk_d1 <= lvds_clk;
    end
end

assign lvds_clk_pos = lvds_clk & ~lvds_clk_d1;  // 上升沿检测
assign lvds_clk_neg = ~lvds_clk & lvds_clk_d1;  // 下降沿检测

// 数据接收逻辑
always @(posedge sys_clk or negedge rst_n) begin
    if (!rst_n) begin
        rx_shift_reg <= 64'd0;
        bit_cnt <= 7'd0;
        rx_valid <= 1'b0;
        rx_data <= 64'd0;
    end else begin
        // 在随路时钟的下降沿接收数据
        if (lvds_clk_neg) begin
            // 移入3位新数据
            rx_shift_reg <= {rx_shift_reg[60:0], lvds_in[3:1]};
            bit_cnt <= bit_cnt + 7'd3;
            
            // 接收完64位数据
            if (bit_cnt >= 7'd61) begin
                bit_cnt <= 7'd0;
                rx_data <= {rx_shift_reg[60:0], lvds_in[3:1]};
                rx_valid <= 1'b1;
            end else begin
                rx_valid <= 1'b0;
            end
        end else begin
            rx_valid <= 1'b0;
        end
    end
end

endmodule
