module lvds_rx_tb;

// 参数定义
parameter SYS_CLK_PERIOD = 8;     // 125MHz系统时钟周期 (ns)
parameter LVDS_CLK_PERIOD = 40;   // 25MHz LVDS时钟周期 (ns)

// 测试激励信号
reg         sys_clk;
reg         rst_n;
reg  [3:0]  lvds_in;
wire        rx_valid;
wire [63:0] rx_data;

// 实例化待测模块
lvds_rx u_lvds_rx (
    .sys_clk     (sys_clk),
    .rst_n       (rst_n),
    .lvds_in     (lvds_in),
    .rx_valid    (rx_valid),
    .rx_data     (rx_data)
);

// 生成系统时钟
initial begin
    sys_clk = 0;
    forever #(SYS_CLK_PERIOD/2) sys_clk = ~sys_clk;
end

// 生成测试激励
initial begin
    // 初始化信号
    rst_n = 0;
    lvds_in = 4'b0000;
    
    // 等待100ns后释放复位
    #96;
    rst_n = 1;
    lvds_in[0] = 0;
    // 发送测试数据
    send_test_data(64'hA5A5_5A5A_1234_5678);
    send_test_data(64'hABCD_A5A5_AAAA_5555);
    
    // 等待一段时间后结束仿真
    #1000;
    $display("Simulation finished!");
    $finish;
end

// 监控接收数据
always @(posedge sys_clk) begin
    if (rx_valid) begin
        $display("Time=%0t ns: Received data = 0x%h", $time, rx_data);
    end
end

// 发送测试数据的任务
task send_test_data;
    input [63:0] test_data;
    integer i;
    begin
        // 发送22个LVDS时钟周期的数据（每个时钟周期发送3位数据）
        for (i = 0; i < 22; i = i + 1) begin
            // LVDS时钟低电平
            // lvds_in[0] = 0;  // LVDS时钟
            lvds_in[3:1] = test_data[((21-i)*3) +: 3];  // 数据位
            #8;
            // #(LVDS_CLK_PERIOD/2);
            
            // LVDS时钟高电平
            lvds_in[0] = 1;  // LVDS时钟
            #16;
            lvds_in[0] = 0;
            #16;
            // #(LVDS_CLK_PERIOD/2);
        end
    end
endtask

// 添加波形导出
initial begin
    $dumpfile("lvds_rx_tb.vcd");
    $dumpvars(0, lvds_rx_tb);
end

endmodule
