module pulse_gen (
    input wire clk,
    input wire rst_n,
    input wire rx_valid,
    input wire data_in,
    output reg pulse_out
);

// 参数定义
// 系统时钟为125MHz，20KHz周期需要6250个时钟周期
localparam PERIOD_CNT = 6250;
// 90%占空比计数值
localparam HIGH_DUTY_CNT = 5625;
// 10%占空比计数值
localparam LOW_DUTY_CNT = 625;

// 寄存器定义
reg data_in_d1;
reg start_flag;
reg [13:0] period_cnt;    // 位宽改为14位以适应6250
reg [3:0] cycle_cnt;
reg high_duty_flag;

reg rx_valid_d1;
wire rx_valid_posedge;
assign rx_valid_posedge = rx_valid && !rx_valid_d1;
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        rx_valid_d1 <= 1'b0;
    else
        rx_valid_d1 <= rx_valid;
end


// 边沿检测,检测到rx_valid边沿时，启动标志置位
wire posedge_detect;
wire negedge_detect;
assign posedge_detect = data_in && !data_in_d1;
assign negedge_detect = !data_in && data_in_d1;
// 边沿检测寄存器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        data_in_d1 <= 1'b0;
    else if (rx_valid_posedge)
        data_in_d1 <= data_in;
end

// 启动标志控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        start_flag <= 1'b0;
    else if (posedge_detect)
        start_flag <= 1'b1;
    else if (negedge_detect)
        start_flag <= 1'b0;
end

// 周期计数器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        period_cnt <= 14'd0;
    else if (!start_flag)
        period_cnt <= 14'd0;
    else if (period_cnt == PERIOD_CNT - 1)
        period_cnt <= 14'd0;
    else
        period_cnt <= period_cnt + 1'b1;
end

// 周期数计数器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        cycle_cnt <= 3'd0;
    else if (!start_flag)
        cycle_cnt <= 3'd0;
    else if (period_cnt == PERIOD_CNT - 1 && cycle_cnt < 4'd9)
        cycle_cnt <= cycle_cnt + 1'b1;
end

// 占空比标志
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        high_duty_flag <= 1'b1;
    else if (!start_flag)
        high_duty_flag <= 1'b1;
    else if (cycle_cnt >= 4'd7)//20KHz,前7个周期占空比为90%，一共350us
        high_duty_flag <= 1'b0;
end

// 输出控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        pulse_out <= 1'b0;
    else if (!start_flag)
        pulse_out <= 1'b0;
    else if (high_duty_flag)
        pulse_out <= (period_cnt < HIGH_DUTY_CNT);
    else
        pulse_out <= (period_cnt < LOW_DUTY_CNT);
end

endmodule
