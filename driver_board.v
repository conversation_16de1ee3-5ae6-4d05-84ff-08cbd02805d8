module driver_board(
    input wire sys_clk,        // 输入时钟
    input wire sys_rst_n,      // 复位信号
    input wire [3:0] lvds_in,   // LVDS输入信号
    input wire valve_sel,      // 选择信号
    output wire [63:0] valve, // 输出通道
    // 新增的SPI信号
    output wire spi_clk,
    output wire spi_cs_n,
    output wire spi_mosi,
    input wire spi_miso
);

parameter SYS_CLK_FREQ = 125_000_000; // 系统时钟频率 125MHz
parameter SPI_CLK_FREQ = 25_000_000;   // SPI时钟频率 25MHz

wire clk_125M;
wire [63:0] rx_data;
wire        rx_valid;
wire [63:0] mac_valve;
wire [63:0] matrix_valve_original;
wire [63:0] matrix_valve_final; // 改为reg类型以便在过程块中赋值

// wire            clock_125M_f;
// GTP_CLKBUFG u_clk125M_bufg (
// .CLKOUT  (clock_125M_f),
// .CLKIN   (sys_clk )
// );

mypll u_mypll (
  .clkin1(sys_clk),        // input
  .pll_lock(pll_lock),    // output
  .clkout0(clk_125M)       // output
);



lvds_rx u_lvds_rx
(
    .sys_clk(clk_125M),    // input
    .rst_n(sys_rst_n),        // input
    .lvds_in(lvds_in),    // input[3:0]
    .rx_valid(rx_valid),  // output
    .rx_data(rx_data)     // output[63:0]
);

valve_select u_valve_select
    (
    .rx_valve(rx_data),          // input[63:0]
    .valve_sel(valve_sel),        // input
    .mac_valve(mac_valve),        // output[63:0]
    .matrix_valve(matrix_valve_original)   // output[63:0]
);

bind_pin u_bind_pin
    (
    .matrix_valve(matrix_valve_final),  // input[63:0]
    .mac_valve(mac_valve),        // input[63:0]
    .valve_sel(valve_sel),        // input
    .valve(valve)                 // output[63:0]
);


spi_master #(
    .SPI_CLK_FREQ(SPI_CLK_FREQ),
    .SYS_CLK_FREQ(SYS_CLK_FREQ)
) u_spi_master
    (
    .sys_clk(clk_125M),            // input
    .rst_n(sys_rst_n),                // input
    .rx_valid(rx_valid),          // input
    .valve_data(rx_data),      // input[63:0]
    .spi_clk(spi_clk),            // output
    .spi_cs_n(spi_cs_n),          // output
    .spi_mosi(spi_mosi),          // output
    .spi_miso(spi_miso),          // input
    .tx_busy(),            // output
    .tx_done()             // output
);

genvar i;
generate
    for (i = 0; i < 64; i = i + 1) begin : gen_pulse
        pulse_gen u_pulse_gen_inst (
            .clk(clk_125M),          // input
            .rst_n(sys_rst_n),          // input
            .rx_valid(rx_valid),    // input
            .data_in(matrix_valve_original[i]),      // input
            .pulse_out(matrix_valve_final[i])   // output
        );
    end
endgenerate

endmodule