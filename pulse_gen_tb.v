`timescale 1ns/1ps

module pulse_gen_tb;

// 时钟周期参数 (125MHz = 8ns)
localparam CLK_PERIOD = 8;
// rx_valid周期 (800us = 100000个时钟周期)
localparam VALID_PERIOD = 100000;

// 信号定义
reg clk;
reg rst_n;
reg rx_valid;
reg data_in;
wire pulse_out;

// 计数器用于产生rx_valid
reg [16:0] valid_cnt;

// 例化被测模块
pulse_gen u_pulse_gen(
    .clk(clk),
    .rst_n(rst_n),
    .rx_valid(rx_valid),
    .data_in(data_in),
    .pulse_out(pulse_out)
);

// 时钟生成
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

// 复位和测试激励
initial begin
    // 初始化信号
    rst_n = 1'b1;
    rx_valid = 1'b0;
    data_in = 1'b0;
    valid_cnt = 17'd0;
    
    // 复位过程
    #100;
    rst_n = 1'b0;
    #200;
    rst_n = 1'b1;
    
    // 等待5000个时钟周期后开始测试
    #40000;
    
    // 波形仿真到2.5ms
    #2500000;
    $finish;
end

// 产生rx_valid信号
always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        valid_cnt <= 17'd0;
    else if (valid_cnt == VALID_PERIOD-1)
        valid_cnt <= 17'd0;
    else
        valid_cnt <= valid_cnt + 1'b1;
end

always @(posedge clk or negedge rst_n) begin
    if (!rst_n)
        rx_valid <= 1'b0;
    else
        rx_valid <= (valid_cnt == 17'd0);
end

// 随机种子初始化
initial begin
    $random(123);  // 设置随机种子
end

// 产生data_in信号
reg [3:0] rand_value;
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        data_in <= 1'b0;
        rand_value <= 4'd0;
    end
    else if (rx_valid) begin
        rand_value <= $random;  // 获取随机值
        // rand_value为0或1时改变当前值，其他值保持不变
        // 这样设置使data_in有较大概率保持当前值
        if (rand_value < 4'd2) begin
            data_in <= ~data_in;
        end
    end
end

// 添加波形输出
initial begin
    $dumpfile("pulse_gen_wave.vcd");
    $dumpvars(0, pulse_gen_tb);
end

endmodule
