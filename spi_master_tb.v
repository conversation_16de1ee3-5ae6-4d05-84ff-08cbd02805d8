// SPI Master 测试平台 (阀控制协议)
`timescale 1ns / 1ps

module spi_master_tb;

// 测试参数
parameter CLK_PERIOD = 8;     // 125MHz时钟周期 = 8ns

// 协议参数
localparam PACKET_BITS = 80;   // 数据包位数

// 测试信号
reg                      sys_clk;
reg                      rst_n;
reg                      rx_valid;
reg [63:0]               valve_data;
wire                     spi_clk;
wire                     spi_cs_n;
wire                     spi_mosi;
reg                      spi_miso;
wire                     tx_busy;
wire                     tx_done;

// 实例化被测模块
spi_master #(
    .SYS_CLK_FREQ   (125_000_000),
    .SPI_CLK_FREQ   (25_000_000)
) uut (
    .sys_clk     (sys_clk),
    .rst_n       (rst_n),
    .rx_valid    (rx_valid),
    .valve_data  (valve_data),
    .spi_clk     (spi_clk),
    .spi_cs_n    (spi_cs_n),
    .spi_mosi    (spi_mosi),
    .spi_miso    (spi_miso),
    .tx_busy     (tx_busy),
    .tx_done     (tx_done)
);

// 时钟生成
initial begin
    sys_clk = 0;
    forever #(CLK_PERIOD/2) sys_clk = ~sys_clk;
end

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    rx_valid = 0;
    valve_data = 0;
    spi_miso = 0;

    // 复位
    #(CLK_PERIOD * 10);
    rst_n = 1;
    #(CLK_PERIOD * 4);

    // 测试1: 控制阀3和阀7开启 (对应协议文档示例)
    $display("Test 1: Opening valve 3 and valve 7");
    valve_data = 64'h0000000000000088;  // bit3=1, bit7=1
    // #(CLK_PERIOD/2);
    rx_valid = 1;
    #(CLK_PERIOD);
    rx_valid = 0;

    // 等待传输完成
    wait(tx_done);
    #(CLK_PERIOD * 10);

    // 测试2: 开启前8个阀
    $display("Test 2: Opening first 8 valves");
    valve_data = 64'h00000000000000FF;  // 前8位全为1
    #(CLK_PERIOD/2);
    rx_valid = 1;
    #(CLK_PERIOD);
    rx_valid = 0;

    // 等待传输完成
    wait(tx_done);
    #(CLK_PERIOD * 10);

    // 测试3: 开启所有奇数位阀 (0, 2, 4, 6, ...)
    $display("Test 3: Opening all odd-numbered valves");
    valve_data = 64'hAAAAAAAAAAAAAAAA;  // 交替位模式
    #(CLK_PERIOD/2);
    rx_valid = 1;
    #(CLK_PERIOD);
    rx_valid = 0;

    // 等待传输完成
    wait(tx_done);
    #(CLK_PERIOD * 10);

    // 测试4: 关闭所有阀
    $display("Test 4: Closing all valves");
    #(CLK_PERIOD/2);
    valve_data = 64'h0000000000000000;  // 全部关闭
    rx_valid = 1;
    #(CLK_PERIOD);
    rx_valid = 0;

    // 等待传输完成
    wait(tx_done);
    #(CLK_PERIOD * 20);

    $display("All valve control tests completed!");
    $finish;
end

// 监控SPI信号和协议解析
reg [79:0] received_packet;
reg [6:0] bit_counter;
reg [7:0] start_flag;
reg [63:0] valve_status;
reg [7:0] received_crc;
integer i;

initial begin
    received_packet = 0;
    bit_counter = 0;
end

// 在SPI时钟上升沿采样数据 (SPI模式0)
always @(posedge spi_clk or posedge spi_cs_n) begin
    if (spi_cs_n) begin
        if (bit_counter == PACKET_BITS) begin
            // 解析数据包
            start_flag = received_packet[79:72];    // 起始标志
            valve_status = received_packet[71:8];   // 阀状态
            received_crc = received_packet[7:0];    // CRC校验

            $display("=== SPI Packet Received at time %t ===", $time);
            $display("Start Flag: 0x%02h (Expected: 0xAA)", start_flag);
            $display("Valve Status: 0x%016h", valve_status);
            $display("CRC: 0x%02h", received_crc);

            // 验证起始标志
            if (start_flag == 8'hAA) begin
                $display("✓ Start flag correct");
            end else begin
                $display("✗ Start flag incorrect!");
            end

            // 显示开启的阀
            $display("Active valves: ");
            for (i = 0; i < 64; i = i + 1) begin
                if (valve_status[i]) begin
                    $display("  Valve %0d: ON", i);
                end
            end
            $display("=====================================");
        end
        bit_counter <= 0;
        received_packet <= 0;
    end else begin
        received_packet <= {received_packet[PACKET_BITS-2:0], spi_mosi};
        bit_counter <= bit_counter + 1;
    end
end

// 波形文件生成
initial begin
    $dumpfile("spi_master_tb.vcd");
    $dumpvars(0, spi_master_tb);
end

// 超时保护
initial begin
    #(CLK_PERIOD * 50000);  // 50000个时钟周期后超时
    $display("ERROR: Simulation timeout!");
    $finish;
end

endmodule
